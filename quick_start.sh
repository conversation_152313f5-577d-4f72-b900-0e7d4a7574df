#!/bin/bash

# EchoNote 快速启动脚本
# 用于快速设置和启动 EchoNote 项目

set -e

echo "🚀 EchoNote 快速启动脚本"
echo "=========================="

# 检查 Docker 是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

# 检查 uv 是否安装
if ! command -v uv &> /dev/null; then
    echo "❌ uv 未安装，请先安装 uv (https://docs.astral.sh/uv/)"
    exit 1
fi

echo "✅ 环境检查通过"

# 创建环境变量文件
if [ ! -f .env ]; then
    echo "📝 创建环境变量文件..."
    cp .env.example .env
    echo "✅ 已创建 .env 文件，您可以根据需要修改配置"
fi

# 启动数据库服务
echo "🐳 启动数据库服务..."
docker-compose up -d postgres redis

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 15

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose ps

# 检查扩展状态
echo "🔍 检查数据库扩展..."
docker exec echo_note-postgres-dev psql -U echo_note -d echo_note -c "SELECT extname, extversion FROM pg_extension WHERE extname IN ('vector', 'age');" || echo "⚠️ 扩展检查失败，稍后会尝试修复"

# 尝试设置AGE扩展（如果需要）
echo "🔧 设置AGE扩展..."
docker exec echo_note-postgres-dev psql -U echo_note -d echo_note -c "LOAD 'age'; SET search_path = ag_catalog, \"\$user\", public; SELECT ag_catalog.create_graph('echo_note_graph');" 2>/dev/null || echo "⚠️ AGE扩展可能需要手动设置，请运行: ./setup_age_manually.sh"

# 安装 Python 依赖
echo "📦 安装 Python 依赖..."
cd apps
uv sync

# 初始化数据库
echo "🗃️ 初始化数据库..."
cd ..
./manage_db.sh init
cd apps

# 启动应用
echo "🎉 启动 EchoNote 应用..."
echo ""
echo "应用将在以下地址启动："
echo "- API 文档: http://localhost:8000/docs"
echo "- 健康检查: http://localhost:8000/health"
echo "- PostgreSQL 管理: http://localhost:5050"
echo "- Redis 管理: http://localhost:8081"
echo ""
echo "按 Ctrl+C 停止应用"
echo ""

uv run python run.py
