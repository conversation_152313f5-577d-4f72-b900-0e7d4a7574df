#!/bin/bash

# 手动设置 Apache AGE 扩展的脚本

set -e

echo "🔧 手动设置 Apache AGE 扩展"
echo "=========================="

# 检查容器是否运行
if ! docker ps | grep -q echo_note-postgres-dev; then
    echo "❌ PostgreSQL 容器未运行，请先启动："
    echo "docker-compose up -d postgres"
    exit 1
fi

echo "📋 当前扩展状态："
docker exec echo_note-postgres-dev psql -U echo_note -d echo_note -c "SELECT extname, extversion FROM pg_extension;"

echo ""
echo "🔍 检查 AGE 扩展是否可用："
docker exec echo_note-postgres-dev psql -U echo_note -d echo_note -c "SELECT * FROM pg_available_extensions WHERE name = 'age';"

echo ""
echo "🔧 尝试手动设置 AGE 扩展："

# 进入容器并执行设置命令
docker exec echo_note-postgres-dev psql -U echo_note -d echo_note << 'EOF'
-- 确保 AGE 扩展已加载
LOAD 'age';

-- 设置搜索路径
SET search_path = ag_catalog, "$user", public;

-- 尝试创建图数据库
DO $$
BEGIN
    BEGIN
        PERFORM ag_catalog.create_graph('echo_note_graph');
        RAISE NOTICE 'Graph echo_note_graph created successfully';
    EXCEPTION WHEN duplicate_object THEN
        RAISE NOTICE 'Graph echo_note_graph already exists';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Failed to create graph: %', SQLERRM;
    END;
END $$;

-- 检查图是否存在
SELECT name FROM ag_catalog.ag_graph;

-- 测试简单的 Cypher 查询
SELECT * FROM ag_catalog.cypher('echo_note_graph', $$
    CREATE (n:TestNode {name: 'test'})
    RETURN n
$$) AS (result agtype);

-- 清理测试节点
SELECT * FROM ag_catalog.cypher('echo_note_graph', $$
    MATCH (n:TestNode)
    DELETE n
$$) AS (result agtype);

EOF

echo ""
echo "✅ AGE 扩展设置完成！"
echo ""
echo "现在可以测试应用的健康检查："
echo "curl http://localhost:8000/health"
