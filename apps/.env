# 应用配置
APP_NAME=EchoNote
APP_VERSION=0.1.0
APP_DESCRIPTION=EchoNote API服务 - 支持向量数据库和图数据库
DEBUG=true
ENVIRONMENT=development

# 服务器配置
HOST=0.0.0.0
PORT=8000

# 数据库配置 (PostgreSQL with pgvector and Apache AGE)
DATABASE_URL=postgresql+asyncpg://echo_note:password@localhost:5432/echo_note
DB_HOST=localhost
DB_PORT=5432
DB_NAME=echo_note
DB_USER=echo_note
DB_PASSWORD=password
DB_ECHO=false
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20

# 测试数据库配置
TEST_DATABASE_URL=postgresql+asyncpg://echo_note:password@localhost:5432/echo_note
TEST_DB_NAME=echo_note

# Redis配置 (with password authentication)
REDIS_URL=redis://:password@localhost:6380/0
REDIS_HOST=localhost
REDIS_PORT=6380
REDIS_DB=0
REDIS_PASSWORD=password
REDIS_MAX_CONNECTIONS=10

# JWT配置
SECRET_KEY=your-secret-key-here-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS配置
ALLOWED_ORIGINS=["http://localhost:3000", "http://localhost:8080"]
ALLOWED_METHODS=["GET", "POST", "PUT", "DELETE", "OPTIONS"]
ALLOWED_HEADERS=["*"]

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# 文件上传配置
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_DIR=uploads

# 向量数据库配置 (pgvector)
VECTOR_DIMENSION=1536  # OpenAI embedding dimension
VECTOR_INDEX_TYPE=hnsw  # HNSW or IVFFlat
VECTOR_DISTANCE_METRIC=cosine  # cosine, l2, inner_product

# 图数据库配置 (Apache AGE)
GRAPH_NAME=echo_note_graph
AGE_SCHEMA=ag_catalog

# Docker网络配置
DOCKER_NETWORK=echo_note-network
POSTGRES_CONTAINER=echo_note-postgres-dev
REDIS_CONTAINER=echo_note-redis-dev
