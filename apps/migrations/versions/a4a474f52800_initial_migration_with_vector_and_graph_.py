"""Initial migration with vector and graph support

Revision ID: a4a474f52800
Revises: 
Create Date: 2025-08-07 11:19:11.449906

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from pgvector.sqlalchemy import VECTOR
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'a4a474f52800'
down_revision: Union[str, Sequence[str], None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('embedding_collections',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False, comment='集合名称'),
    sa.Column('description', sa.Text(), nullable=True, comment='描述'),
    sa.Column('dimension', sa.Integer(), nullable=False, comment='向量维度'),
    sa.Column('distance_metric', sa.String(length=20), nullable=False, comment='距离度量方式'),
    sa.Column('embedding_count', sa.Integer(), nullable=False, comment='向量数量'),
    sa.Column('meta_data', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='元数据'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_index(op.f('ix_embedding_collections_id'), 'embedding_collections', ['id'], unique=False)
    op.create_table('embeddings',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('content', sa.Text(), nullable=False, comment='原始内容'),
    sa.Column('title', sa.String(length=255), nullable=True, comment='标题'),
    sa.Column('embedding', VECTOR(dim=1536), nullable=True, comment='向量嵌入'),
    sa.Column('meta_data', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='元数据'),
    sa.Column('source', sa.String(length=255), nullable=True, comment='来源'),
    sa.Column('source_id', sa.String(length=255), nullable=True, comment='来源ID'),
    sa.Column('category', sa.String(length=100), nullable=True, comment='分类'),
    sa.Column('tags', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='标签'),
    sa.Column('token_count', sa.Integer(), nullable=True, comment='token数量'),
    sa.Column('char_count', sa.Integer(), nullable=True, comment='字符数量'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_embeddings_id'), 'embeddings', ['id'], unique=False)
    op.create_table('graph_edges',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('graph_name', sa.String(length=100), nullable=False, comment='图名称'),
    sa.Column('edge_id', sa.String(length=255), nullable=False, comment='边ID'),
    sa.Column('label', sa.String(length=100), nullable=False, comment='边标签'),
    sa.Column('source_node_id', sa.String(length=255), nullable=False, comment='源节点ID'),
    sa.Column('target_node_id', sa.String(length=255), nullable=False, comment='目标节点ID'),
    sa.Column('properties', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='边属性'),
    sa.Column('weight', sa.Float(), nullable=True, comment='边权重'),
    sa.Column('is_directed', sa.Boolean(), nullable=False, comment='是否有向'),
    sa.Column('meta_data', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='元数据'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_graph_edges_id'), 'graph_edges', ['id'], unique=False)
    op.create_table('graph_nodes',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('graph_name', sa.String(length=100), nullable=False, comment='图名称'),
    sa.Column('node_id', sa.String(length=255), nullable=False, comment='节点ID'),
    sa.Column('label', sa.String(length=100), nullable=False, comment='节点标签'),
    sa.Column('name', sa.String(length=255), nullable=True, comment='节点名称'),
    sa.Column('properties', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='节点属性'),
    sa.Column('meta_data', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='元数据'),
    sa.Column('in_degree', sa.Integer(), nullable=False, comment='入度'),
    sa.Column('out_degree', sa.Integer(), nullable=False, comment='出度'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_graph_nodes_id'), 'graph_nodes', ['id'], unique=False)
    op.create_table('graph_schemas',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('graph_name', sa.String(length=100), nullable=False, comment='图名称'),
    sa.Column('description', sa.Text(), nullable=True, comment='图描述'),
    sa.Column('node_labels', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='节点标签列表'),
    sa.Column('edge_labels', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='边标签列表'),
    sa.Column('schema_definition', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='模式定义'),
    sa.Column('node_count', sa.Integer(), nullable=False, comment='节点数量'),
    sa.Column('edge_count', sa.Integer(), nullable=False, comment='边数量'),
    sa.Column('is_active', sa.Boolean(), nullable=False, comment='是否激活'),
    sa.Column('meta_data', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='元数据'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('graph_name')
    )
    op.create_index(op.f('ix_graph_schemas_id'), 'graph_schemas', ['id'], unique=False)
    op.create_table('users',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('username', sa.String(length=50), nullable=False),
    sa.Column('email', sa.String(length=100), nullable=False),
    sa.Column('full_name', sa.String(length=100), nullable=True),
    sa.Column('hashed_password', sa.String(length=255), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('is_superuser', sa.Boolean(), nullable=False),
    sa.Column('is_verified', sa.Boolean(), nullable=False),
    sa.Column('avatar_url', sa.String(length=255), nullable=True),
    sa.Column('bio', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('last_login_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_users_username'), table_name='users')
    op.drop_index(op.f('ix_users_id'), table_name='users')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_table('users')
    op.drop_index(op.f('ix_graph_schemas_id'), table_name='graph_schemas')
    op.drop_table('graph_schemas')
    op.drop_index(op.f('ix_graph_nodes_id'), table_name='graph_nodes')
    op.drop_table('graph_nodes')
    op.drop_index(op.f('ix_graph_edges_id'), table_name='graph_edges')
    op.drop_table('graph_edges')
    op.drop_index(op.f('ix_embeddings_id'), table_name='embeddings')
    op.drop_table('embeddings')
    op.drop_index(op.f('ix_embedding_collections_id'), table_name='embedding_collections')
    op.drop_table('embedding_collections')
    # ### end Alembic commands ###
