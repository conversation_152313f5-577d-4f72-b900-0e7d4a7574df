#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/7 11:00
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : embedding.py
# @Update  : 2025/8/7 11:00 向量嵌入模型

from datetime import datetime
from typing import Optional, List
from sqlalchemy import String, DateTime, Text, Integer
from sqlalchemy.orm import Mapped, mapped_column
from sqlalchemy.dialects.postgresql import JSONB
from pgvector.sqlalchemy import Vector
from configs.database import Base
from configs.settings import settings


class Embedding(Base):
    """向量嵌入模型"""
    
    __tablename__ = "embeddings"
    
    # 主键
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    
    # 内容
    content: Mapped[str] = mapped_column(Text, nullable=False, comment="原始内容")
    title: Mapped[Optional[str]] = mapped_column(String(255), nullable=True, comment="标题")
    
    # 向量嵌入
    embedding: Mapped[List[float]] = mapped_column(
        Vector(settings.vector_dimension), 
        nullable=True,
        comment="向量嵌入"
    )
    
    # 元数据
    meta_data: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True, comment="元数据")
    source: Mapped[Optional[str]] = mapped_column(String(255), nullable=True, comment="来源")
    source_id: Mapped[Optional[str]] = mapped_column(String(255), nullable=True, comment="来源ID")
    
    # 分类和标签
    category: Mapped[Optional[str]] = mapped_column(String(100), nullable=True, comment="分类")
    tags: Mapped[Optional[List[str]]] = mapped_column(JSONB, nullable=True, comment="标签")
    
    # 统计信息
    token_count: Mapped[Optional[int]] = mapped_column(Integer, nullable=True, comment="token数量")
    char_count: Mapped[Optional[int]] = mapped_column(Integer, nullable=True, comment="字符数量")
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow, 
        nullable=False,
        comment="创建时间"
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow, 
        onupdate=datetime.utcnow, 
        nullable=False,
        comment="更新时间"
    )
    
    def __repr__(self) -> str:
        return f"<Embedding(id={self.id}, title='{self.title}', source='{self.source}')>"
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "content": self.content,
            "title": self.title,
            "embedding": self.embedding,
            "meta_data": self.meta_data,
            "source": self.source,
            "source_id": self.source_id,
            "category": self.category,
            "tags": self.tags,
            "token_count": self.token_count,
            "char_count": self.char_count,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
    
    @classmethod
    def calculate_char_count(cls, content: str) -> int:
        """计算字符数量"""
        return len(content)
    
    @classmethod
    def estimate_token_count(cls, content: str) -> int:
        """估算token数量（简单估算，实际应该使用tokenizer）"""
        # 简单估算：中文1个字符约等于1个token，英文4个字符约等于1个token
        chinese_chars = sum(1 for char in content if '\u4e00' <= char <= '\u9fff')
        other_chars = len(content) - chinese_chars
        return chinese_chars + (other_chars // 4)


class EmbeddingCollection(Base):
    """向量集合模型"""
    
    __tablename__ = "embedding_collections"
    
    # 主键
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    
    # 基本信息
    name: Mapped[str] = mapped_column(String(100), unique=True, nullable=False, comment="集合名称")
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="描述")
    
    # 配置
    dimension: Mapped[int] = mapped_column(Integer, nullable=False, comment="向量维度")
    distance_metric: Mapped[str] = mapped_column(
        String(20), 
        default="cosine", 
        nullable=False,
        comment="距离度量方式"
    )
    
    # 统计信息
    embedding_count: Mapped[int] = mapped_column(Integer, default=0, nullable=False, comment="向量数量")
    
    # 元数据
    meta_data: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True, comment="元数据")
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow, 
        nullable=False,
        comment="创建时间"
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow, 
        onupdate=datetime.utcnow, 
        nullable=False,
        comment="更新时间"
    )
    
    def __repr__(self) -> str:
        return f"<EmbeddingCollection(id={self.id}, name='{self.name}', count={self.embedding_count})>"
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "dimension": self.dimension,
            "distance_metric": self.distance_metric,
            "embedding_count": self.embedding_count,
            "meta_data": self.meta_data,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
