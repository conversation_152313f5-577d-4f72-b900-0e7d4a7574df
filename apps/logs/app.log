2025-08-07 11:37:16 - uvicorn.error - INFO - server - _serve:84 - Started server process [1967]
2025-08-07 11:37:16 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-07 11:37:16 - main - INFO - main - lifespan:36 - 正在启动EchoNote应用...
2025-08-07 11:37:16 - main - ERROR - main - lifespan:70 - 应用启动失败: Multiple exceptions: [Errno 61] Connect call failed ('::1', 5433, 0, 0), [<PERSON><PERSON><PERSON> 61] Connect call failed ('127.0.0.1', 5433)
2025-08-07 11:37:16 - uvicorn.error - ERROR - on - send:121 - Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/starlette/routing.py", line 694, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/main.py", line 41, in lifespan
    async with engine.begin() as conn:
  File "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/ext/asyncio/engine.py", line 1066, in begin
    async with conn:
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/ext/asyncio/base.py", line 121, in __aenter__
    return await self.start(is_ctxmanager=True)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/ext/asyncio/engine.py", line 274, in start
    await greenlet_spawn(self.sync_engine.connect)
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 201, in greenlet_spawn
    result = context.throw(*sys.exc_info())
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 3271, in connect
    return self._connection_cls(self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 143, in __init__
    self._dbapi_connection = engine.raw_connection()
                             ^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 3295, in raw_connection
    return self.pool.connect()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/pool/base.py", line 447, in connect
    return _ConnectionFairy._checkout(self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/pool/base.py", line 1264, in _checkout
    fairy = _ConnectionRecord.checkout(pool)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/pool/base.py", line 711, in checkout
    rec = pool._do_get()
          ^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/pool/impl.py", line 177, in _do_get
    with util.safe_reraise():
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/util/langhelpers.py", line 224, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/pool/impl.py", line 175, in _do_get
    return self._create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/pool/base.py", line 388, in _create_connection
    return _ConnectionRecord(self)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/pool/base.py", line 673, in __init__
    self.__connect()
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/pool/base.py", line 899, in __connect
    with util.safe_reraise():
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/util/langhelpers.py", line 224, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/pool/base.py", line 895, in __connect
    self.dbapi_connection = connection = pool._invoke_creator(self)
                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/engine/create.py", line 646, in connect
    return dialect.connect(*cargs, **cparams)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/engine/default.py", line 626, in connect
    return self.loaded_dbapi.connect(*cargs, **cparams)  # type: ignore[no-any-return]  # NOQA: E501
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 964, in connect
    await_only(creator_fn(*arg, **kw)),
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
            ^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/asyncpg/connection.py", line 2421, in connect
    return await connect_utils._connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/asyncpg/connect_utils.py", line 1075, in _connect
    raise last_error or exceptions.TargetServerAttributeNotMatched(
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/asyncpg/connect_utils.py", line 1049, in _connect
    conn = await _connect_addr(
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/asyncpg/connect_utils.py", line 886, in _connect_addr
    return await __connect_addr(params, True, *args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/asyncpg/connect_utils.py", line 931, in __connect_addr
    tr, pr = await connector
             ^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/asyncpg/connect_utils.py", line 802, in _create_ssl_connection
    tr, pr = await loop.create_connection(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_events.py", line 1130, in create_connection
    raise OSError('Multiple exceptions: {}'.format(
OSError: Multiple exceptions: [Errno 61] Connect call failed ('::1', 5433, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 5433)

2025-08-07 11:37:16 - uvicorn.error - ERROR - on - startup:59 - Application startup failed. Exiting.
2025-08-07 11:41:35 - uvicorn.error - INFO - server - _serve:84 - Started server process [4176]
2025-08-07 11:41:35 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-07 11:41:35 - main - INFO - main - lifespan:36 - 正在启动EchoNote应用...
2025-08-07 11:41:35 - main - ERROR - main - lifespan:70 - 应用启动失败: Multiple exceptions: [Errno 61] Connect call failed ('::1', 5433, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 5433)
2025-08-07 11:41:35 - uvicorn.error - ERROR - on - send:121 - Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/starlette/routing.py", line 694, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/main.py", line 41, in lifespan
    async with engine.begin() as conn:
  File "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/ext/asyncio/engine.py", line 1066, in begin
    async with conn:
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/ext/asyncio/base.py", line 121, in __aenter__
    return await self.start(is_ctxmanager=True)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/ext/asyncio/engine.py", line 274, in start
    await greenlet_spawn(self.sync_engine.connect)
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 201, in greenlet_spawn
    result = context.throw(*sys.exc_info())
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 3271, in connect
    return self._connection_cls(self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 143, in __init__
    self._dbapi_connection = engine.raw_connection()
                             ^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 3295, in raw_connection
    return self.pool.connect()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/pool/base.py", line 447, in connect
    return _ConnectionFairy._checkout(self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/pool/base.py", line 1264, in _checkout
    fairy = _ConnectionRecord.checkout(pool)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/pool/base.py", line 711, in checkout
    rec = pool._do_get()
          ^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/pool/impl.py", line 177, in _do_get
    with util.safe_reraise():
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/util/langhelpers.py", line 224, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/pool/impl.py", line 175, in _do_get
    return self._create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/pool/base.py", line 388, in _create_connection
    return _ConnectionRecord(self)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/pool/base.py", line 673, in __init__
    self.__connect()
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/pool/base.py", line 899, in __connect
    with util.safe_reraise():
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/util/langhelpers.py", line 224, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/pool/base.py", line 895, in __connect
    self.dbapi_connection = connection = pool._invoke_creator(self)
                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/engine/create.py", line 646, in connect
    return dialect.connect(*cargs, **cparams)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/engine/default.py", line 626, in connect
    return self.loaded_dbapi.connect(*cargs, **cparams)  # type: ignore[no-any-return]  # NOQA: E501
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 964, in connect
    await_only(creator_fn(*arg, **kw)),
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
            ^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/asyncpg/connection.py", line 2421, in connect
    return await connect_utils._connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/asyncpg/connect_utils.py", line 1075, in _connect
    raise last_error or exceptions.TargetServerAttributeNotMatched(
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/asyncpg/connect_utils.py", line 1049, in _connect
    conn = await _connect_addr(
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/asyncpg/connect_utils.py", line 886, in _connect_addr
    return await __connect_addr(params, True, *args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/asyncpg/connect_utils.py", line 931, in __connect_addr
    tr, pr = await connector
             ^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/asyncpg/connect_utils.py", line 802, in _create_ssl_connection
    tr, pr = await loop.create_connection(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_events.py", line 1130, in create_connection
    raise OSError('Multiple exceptions: {}'.format(
OSError: Multiple exceptions: [Errno 61] Connect call failed ('::1', 5433, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 5433)

2025-08-07 11:41:35 - uvicorn.error - ERROR - on - startup:59 - Application startup failed. Exiting.
2025-08-07 11:41:59 - uvicorn.error - INFO - server - _serve:84 - Started server process [4402]
2025-08-07 11:41:59 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-07 11:41:59 - main - INFO - main - lifespan:36 - 正在启动EchoNote应用...
2025-08-07 11:41:59 - main - ERROR - main - lifespan:70 - 应用启动失败: Multiple exceptions: [Errno 61] Connect call failed ('::1', 5433, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 5433)
2025-08-07 11:41:59 - uvicorn.error - ERROR - on - send:121 - Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/starlette/routing.py", line 694, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/main.py", line 41, in lifespan
    async with engine.begin() as conn:
  File "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/ext/asyncio/engine.py", line 1066, in begin
    async with conn:
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/ext/asyncio/base.py", line 121, in __aenter__
    return await self.start(is_ctxmanager=True)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/ext/asyncio/engine.py", line 274, in start
    await greenlet_spawn(self.sync_engine.connect)
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 201, in greenlet_spawn
    result = context.throw(*sys.exc_info())
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 3271, in connect
    return self._connection_cls(self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 143, in __init__
    self._dbapi_connection = engine.raw_connection()
                             ^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 3295, in raw_connection
    return self.pool.connect()
           ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/pool/base.py", line 447, in connect
    return _ConnectionFairy._checkout(self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/pool/base.py", line 1264, in _checkout
    fairy = _ConnectionRecord.checkout(pool)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/pool/base.py", line 711, in checkout
    rec = pool._do_get()
          ^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/pool/impl.py", line 177, in _do_get
    with util.safe_reraise():
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/util/langhelpers.py", line 224, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/pool/impl.py", line 175, in _do_get
    return self._create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/pool/base.py", line 388, in _create_connection
    return _ConnectionRecord(self)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/pool/base.py", line 673, in __init__
    self.__connect()
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/pool/base.py", line 899, in __connect
    with util.safe_reraise():
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/util/langhelpers.py", line 224, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/pool/base.py", line 895, in __connect
    self.dbapi_connection = connection = pool._invoke_creator(self)
                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/engine/create.py", line 646, in connect
    return dialect.connect(*cargs, **cparams)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/engine/default.py", line 626, in connect
    return self.loaded_dbapi.connect(*cargs, **cparams)  # type: ignore[no-any-return]  # NOQA: E501
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 964, in connect
    await_only(creator_fn(*arg, **kw)),
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
            ^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/asyncpg/connection.py", line 2421, in connect
    return await connect_utils._connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/asyncpg/connect_utils.py", line 1075, in _connect
    raise last_error or exceptions.TargetServerAttributeNotMatched(
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/asyncpg/connect_utils.py", line 1049, in _connect
    conn = await _connect_addr(
           ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/asyncpg/connect_utils.py", line 886, in _connect_addr
    return await __connect_addr(params, True, *args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/asyncpg/connect_utils.py", line 931, in __connect_addr
    tr, pr = await connector
             ^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/asyncpg/connect_utils.py", line 802, in _create_ssl_connection
    tr, pr = await loop.create_connection(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/asyncio/base_events.py", line 1130, in create_connection
    raise OSError('Multiple exceptions: {}'.format(
OSError: Multiple exceptions: [Errno 61] Connect call failed ('::1', 5433, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 5433)

2025-08-07 11:41:59 - uvicorn.error - ERROR - on - startup:59 - Application startup failed. Exiting.
2025-08-07 11:42:16 - uvicorn.error - INFO - server - _serve:84 - Started server process [4548]
2025-08-07 11:42:16 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-07 11:42:16 - main - INFO - main - lifespan:36 - 正在启动EchoNote应用...
2025-08-07 11:42:16 - main - INFO - main - lifespan:42 - 数据库连接成功
2025-08-07 11:42:16 - main - INFO - main - lifespan:47 - pgvector扩展已启用
2025-08-07 11:42:16 - main - INFO - main - lifespan:54 - Apache AGE扩展已启用
2025-08-07 11:42:16 - main - INFO - main - lifespan:60 - Redis连接成功
2025-08-07 11:42:16 - main - INFO - main - lifespan:65 - 数据库连接验证完成，请使用 Alembic 管理数据库迁移
2025-08-07 11:42:16 - main - INFO - main - lifespan:67 - EchoNote应用启动完成
2025-08-07 11:42:16 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-07 11:42:48 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-07 11:42:48 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-07 11:42:48 - main - INFO - main - lifespan:76 - 正在关闭EchoNote应用...
2025-08-07 11:42:48 - main - INFO - main - lifespan:81 - 数据库连接已关闭
2025-08-07 11:42:48 - main - INFO - main - lifespan:85 - Redis连接已关闭
2025-08-07 11:42:48 - main - INFO - main - lifespan:87 - EchoNote应用关闭完成
2025-08-07 11:42:48 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-07 11:42:48 - uvicorn.error - INFO - server - _serve:94 - Finished server process [4548]
2025-08-07 11:42:49 - uvicorn.error - INFO - server - _serve:84 - Started server process [4840]
2025-08-07 11:42:49 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-07 11:42:49 - main - INFO - main - lifespan:36 - 正在启动EchoNote应用...
2025-08-07 11:42:50 - main - INFO - main - lifespan:42 - 数据库连接成功
2025-08-07 11:42:50 - main - INFO - main - lifespan:47 - pgvector扩展已启用
2025-08-07 11:42:50 - main - INFO - main - lifespan:54 - Apache AGE扩展已启用
2025-08-07 11:42:50 - main - INFO - main - lifespan:60 - Redis连接成功
2025-08-07 11:42:50 - main - INFO - main - lifespan:65 - 数据库连接验证完成，请使用 Alembic 管理数据库迁移
2025-08-07 11:42:50 - main - INFO - main - lifespan:67 - EchoNote应用启动完成
2025-08-07 11:42:50 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-07 11:42:54 - uvicorn.error - ERROR - h11_impl - run_asgi:408 - Exception in ASGI application
Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/uvicorn/protocols/http/h11_impl.py", line 403, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/starlette/middleware/exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/starlette/routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/starlette/routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/starlette/routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/starlette/routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/starlette/routing.py", line 75, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/fastapi/routing.py", line 302, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/fastapi/routing.py", line 213, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/main.py", line 126, in root
    "environment": settings.environment
                   ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/EchoNote/apps/.venv/lib/python3.12/site-packages/pydantic/main.py", line 991, in __getattr__
    raise AttributeError(f'{type(self).__name__!r} object has no attribute {item!r}')
AttributeError: 'Settings' object has no attribute 'environment'
2025-08-07 11:43:55 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-07 11:43:55 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-07 11:43:55 - main - INFO - main - lifespan:76 - 正在关闭EchoNote应用...
2025-08-07 11:43:55 - main - INFO - main - lifespan:81 - 数据库连接已关闭
2025-08-07 11:43:55 - main - INFO - main - lifespan:85 - Redis连接已关闭
2025-08-07 11:43:55 - main - INFO - main - lifespan:87 - EchoNote应用关闭完成
2025-08-07 11:43:55 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-07 11:43:55 - uvicorn.error - INFO - server - _serve:94 - Finished server process [4840]
2025-08-07 11:43:56 - uvicorn.error - INFO - server - _serve:84 - Started server process [5431]
2025-08-07 11:43:56 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-07 11:43:56 - main - INFO - main - lifespan:36 - 正在启动EchoNote应用...
2025-08-07 11:43:56 - main - INFO - main - lifespan:42 - 数据库连接成功
2025-08-07 11:43:56 - main - INFO - main - lifespan:47 - pgvector扩展已启用
2025-08-07 11:43:56 - main - INFO - main - lifespan:54 - Apache AGE扩展已启用
2025-08-07 11:43:56 - main - INFO - main - lifespan:60 - Redis连接成功
2025-08-07 11:43:56 - main - INFO - main - lifespan:65 - 数据库连接验证完成，请使用 Alembic 管理数据库迁移
2025-08-07 11:43:56 - main - INFO - main - lifespan:67 - EchoNote应用启动完成
2025-08-07 11:43:56 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-07 11:44:15 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-07 11:44:15 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-07 11:44:15 - main - INFO - main - lifespan:76 - 正在关闭EchoNote应用...
2025-08-07 11:44:15 - main - INFO - main - lifespan:81 - 数据库连接已关闭
2025-08-07 11:44:15 - main - INFO - main - lifespan:85 - Redis连接已关闭
2025-08-07 11:44:15 - main - INFO - main - lifespan:87 - EchoNote应用关闭完成
2025-08-07 11:44:15 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-07 11:44:15 - uvicorn.error - INFO - server - _serve:94 - Finished server process [5431]
2025-08-07 11:44:16 - uvicorn.error - INFO - server - _serve:84 - Started server process [5620]
2025-08-07 11:44:16 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-07 11:44:16 - main - INFO - main - lifespan:36 - 正在启动EchoNote应用...
2025-08-07 11:44:16 - main - INFO - main - lifespan:42 - 数据库连接成功
2025-08-07 11:44:16 - main - INFO - main - lifespan:47 - pgvector扩展已启用
2025-08-07 11:44:16 - main - INFO - main - lifespan:54 - Apache AGE扩展已启用
2025-08-07 11:44:16 - main - INFO - main - lifespan:60 - Redis连接成功
2025-08-07 11:44:16 - main - INFO - main - lifespan:65 - 数据库连接验证完成，请使用 Alembic 管理数据库迁移
2025-08-07 11:44:16 - main - INFO - main - lifespan:67 - EchoNote应用启动完成
2025-08-07 11:44:16 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-07 11:44:54 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-07 11:44:54 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-07 11:44:54 - main - INFO - main - lifespan:76 - 正在关闭EchoNote应用...
2025-08-07 11:44:54 - main - INFO - main - lifespan:81 - 数据库连接已关闭
2025-08-07 11:44:54 - main - INFO - main - lifespan:85 - Redis连接已关闭
2025-08-07 11:44:54 - main - INFO - main - lifespan:87 - EchoNote应用关闭完成
2025-08-07 11:44:54 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-07 11:44:54 - uvicorn.error - INFO - server - _serve:94 - Finished server process [5620]
2025-08-07 11:44:56 - uvicorn.error - INFO - server - _serve:84 - Started server process [5958]
2025-08-07 11:44:56 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-07 11:44:56 - main - INFO - main - lifespan:36 - 正在启动EchoNote应用...
2025-08-07 11:44:56 - main - INFO - main - lifespan:42 - 数据库连接成功
2025-08-07 11:44:56 - main - INFO - main - lifespan:47 - pgvector扩展已启用
2025-08-07 11:44:56 - main - INFO - main - lifespan:54 - Apache AGE扩展已启用
2025-08-07 11:44:56 - main - WARNING - main - lifespan:62 - Redis连接失败
2025-08-07 11:44:56 - main - INFO - main - lifespan:65 - 数据库连接验证完成，请使用 Alembic 管理数据库迁移
2025-08-07 11:44:56 - main - INFO - main - lifespan:67 - EchoNote应用启动完成
2025-08-07 11:44:56 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-07 11:45:34 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-07 11:45:34 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-07 11:45:34 - main - INFO - main - lifespan:76 - 正在关闭EchoNote应用...
2025-08-07 11:45:34 - main - INFO - main - lifespan:81 - 数据库连接已关闭
2025-08-07 11:45:34 - main - INFO - main - lifespan:85 - Redis连接已关闭
2025-08-07 11:45:34 - main - INFO - main - lifespan:87 - EchoNote应用关闭完成
2025-08-07 11:45:34 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-07 11:45:34 - uvicorn.error - INFO - server - _serve:94 - Finished server process [5958]
2025-08-07 11:45:35 - uvicorn.error - INFO - server - _serve:84 - Started server process [6324]
2025-08-07 11:45:35 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-07 11:45:35 - echonote.app - INFO - main - lifespan:31 - 正在启动EchoNote应用...
2025-08-07 11:45:35 - echonote.app - INFO - main - lifespan:37 - 数据库连接成功
2025-08-07 11:45:35 - echonote.app - INFO - main - lifespan:42 - pgvector扩展已启用
2025-08-07 11:45:35 - echonote.app - INFO - main - lifespan:49 - Apache AGE扩展已启用
2025-08-07 11:45:35 - echonote.app - INFO - main - lifespan:55 - Redis连接成功
2025-08-07 11:45:35 - echonote.app - INFO - main - lifespan:60 - 数据库连接验证完成，请使用 Alembic 管理数据库迁移
2025-08-07 11:45:35 - echonote.app - INFO - main - lifespan:62 - EchoNote应用启动完成
2025-08-07 11:45:35 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-07 11:46:07 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-07 11:46:07 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-07 11:46:07 - echonote.app - INFO - main - lifespan:71 - 正在关闭EchoNote应用...
2025-08-07 11:46:07 - echonote.app - INFO - main - lifespan:76 - 数据库连接已关闭
2025-08-07 11:46:07 - echonote.app - INFO - main - lifespan:80 - Redis连接已关闭
2025-08-07 11:46:07 - echonote.app - INFO - main - lifespan:82 - EchoNote应用关闭完成
2025-08-07 11:46:07 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-07 11:46:07 - uvicorn.error - INFO - server - _serve:94 - Finished server process [6324]
2025-08-07 11:46:08 - uvicorn.error - INFO - server - _serve:84 - Started server process [6603]
2025-08-07 11:46:08 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-07 11:46:08 - echonote.app - INFO - main - lifespan:31 - 正在启动EchoNote应用...
2025-08-07 11:46:08 - echonote.app - INFO - main - lifespan:37 - 数据库连接成功
2025-08-07 11:46:08 - echonote.app - INFO - main - lifespan:42 - pgvector扩展已启用
2025-08-07 11:46:08 - echonote.app - INFO - main - lifespan:49 - Apache AGE扩展已启用
2025-08-07 11:46:08 - echonote.app - INFO - main - lifespan:55 - Redis连接成功
2025-08-07 11:46:08 - echonote.app - INFO - main - lifespan:60 - 数据库连接验证完成，请使用 Alembic 管理数据库迁移
2025-08-07 11:46:08 - echonote.app - INFO - main - lifespan:62 - EchoNote应用启动完成
2025-08-07 11:46:08 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
