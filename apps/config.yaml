# EchoNote 应用配置文件
# 所有配置都在这个文件中管理，不再使用 .env 文件

# 应用基本配置
app:
  name: "EchoNote"
  version: "0.1.0"
  description: "EchoNote API服务 - 支持向量数据库和图数据库"
  debug: true

# 服务器配置
server:
  host: "0.0.0.0"
  port: 8000

# 数据库配置 (PostgreSQL with pgvector and Apache AGE)
database:
  host: "localhost"
  port: 5432
  name: "echo_note"
  user: "echo_note"
  password: "password"
  echo: false
  pool_size: 10
  max_overflow: 20
  
  # 测试数据库配置
  test_name: "echo_note_test"

# Redis配置 (with password authentication)
redis:
  host: "localhost"
  port: 6380
  db: 0
  password: "password"
  max_connections: 10

# JWT配置
jwt:
  secret_key: "your-secret-key-here-change-in-production"
  algorithm: "HS256"
  access_token_expire_minutes: 30

# CORS配置
cors:
  allowed_origins:
    - "http://localhost:3000"
    - "http://localhost:8080"
    - "http://127.0.0.1:3000"
    - "http://127.0.0.1:8080"
    - "http://localhost:5173"  # Vite默认端口
    - "http://127.0.0.1:5173"
  allowed_methods:
    - "GET"
    - "POST"
    - "PUT"
    - "DELETE"
    - "OPTIONS"
  allowed_headers:
    - "*"

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  log_dir: "logs"
  max_file_size: 10485760  # 10MB
  backup_count: 5
  enable_file_logging: true
  enable_console_logging: true

# 文件上传配置
upload:
  max_file_size: 10485760  # 10MB
  upload_dir: "uploads"

# 向量数据库配置 (pgvector)
vector:
  dimension: 1536  # OpenAI embedding dimension
  index_type: "hnsw"  # HNSW or IVFFlat
  distance_metric: "cosine"  # cosine, l2, inner_product

# 图数据库配置 (Apache AGE)
graph:
  name: "echo_note_graph"
  age_schema: "ag_catalog"

# Docker配置
docker:
  network: "echo_note-network"
  postgres_container: "echo_note-postgres-dev"
  redis_container: "echo_note-redis-dev"
