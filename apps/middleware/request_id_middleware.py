#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/7 12:50
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : request_id_middleware.py
# @Update  : 2025/8/7 12:50 请求ID中间件

import uuid
import time
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from contexts import (
    RequestContext,
    set_request_context,
    clear_request_context,
    create_request_context,
    get_request_context
)
from configs.logging_config import get_api_logger

logger = get_api_logger()


class RequestIDMiddleware(BaseHTTPMiddleware):
    """请求ID中间件
    
    功能：
    1. 为每个请求生成唯一的请求ID
    2. 从请求头中提取现有的请求ID（如果存在）
    3. 将请求ID添加到响应头
    4. 设置请求上下文信息
    5. 记录请求日志
    """
    
    def __init__(
        self,
        app,
        request_id_header: str = "X-Request-ID",
        trace_id_header: str = "X-Trace-ID",
        correlation_id_header: str = "X-Correlation-ID",
        generate_if_missing: bool = True,
        log_requests: bool = True
    ):
        super().__init__(app)
        self.request_id_header = request_id_header
        self.trace_id_header = trace_id_header
        self.correlation_id_header = correlation_id_header
        self.generate_if_missing = generate_if_missing
        self.log_requests = log_requests
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求"""
        start_time = time.time()
        
        # 获取或生成请求ID
        request_id = self._get_or_generate_request_id(request)
        trace_id = self._get_header_value(request, self.trace_id_header)
        correlation_id = self._get_header_value(request, self.correlation_id_header)
        
        # 获取客户端信息
        client_ip = self._get_client_ip(request)
        user_agent = request.headers.get("user-agent", "")
        
        # 创建请求上下文
        context = create_request_context(
            request_id=request_id,
            ip_address=client_ip,
            user_agent=user_agent,
            method=request.method,
            path=str(request.url.path),
            query_params=dict(request.query_params),
            headers=dict(request.headers),
            trace_id=trace_id,
            correlation_id=correlation_id
        )
        
        # 设置请求上下文
        set_request_context(context)
        
        # 记录请求开始日志
        if self.log_requests:
            logger.info(
                f"Request started: {request.method} {request.url.path}",
                extra={
                    "request_id": request_id,
                    "method": request.method,
                    "path": str(request.url.path),
                    "query_params": dict(request.query_params),
                    "client_ip": client_ip,
                    "user_agent": user_agent,
                    "trace_id": trace_id,
                    "correlation_id": correlation_id
                }
            )
        
        try:
            # 处理请求
            response = await call_next(request)
            
            # 计算处理时间
            process_time = time.time() - start_time
            
            # 添加请求ID到响应头
            response.headers[self.request_id_header] = request_id
            if trace_id:
                response.headers[self.trace_id_header] = trace_id
            if correlation_id:
                response.headers[self.correlation_id_header] = correlation_id
            
            # 添加处理时间到响应头
            response.headers["X-Process-Time"] = str(round(process_time, 4))
            
            # 记录请求完成日志
            if self.log_requests:
                logger.info(
                    f"Request completed: {request.method} {request.url.path} - {response.status_code}",
                    extra={
                        "request_id": request_id,
                        "method": request.method,
                        "path": str(request.url.path),
                        "status_code": response.status_code,
                        "process_time": process_time,
                        "client_ip": client_ip,
                        "trace_id": trace_id,
                        "correlation_id": correlation_id
                    }
                )
            
            return response
            
        except Exception as e:
            # 计算处理时间
            process_time = time.time() - start_time
            
            # 记录错误日志
            logger.error(
                f"Request failed: {request.method} {request.url.path} - {str(e)}",
                extra={
                    "request_id": request_id,
                    "method": request.method,
                    "path": str(request.url.path),
                    "error": str(e),
                    "process_time": process_time,
                    "client_ip": client_ip,
                    "trace_id": trace_id,
                    "correlation_id": correlation_id
                },
                exc_info=True
            )
            
            # 重新抛出异常
            raise
            
        finally:
            # 清理请求上下文
            clear_request_context()
    
    def _get_or_generate_request_id(self, request: Request) -> str:
        """获取或生成请求ID"""
        request_id = self._get_header_value(request, self.request_id_header)
        
        if not request_id and self.generate_if_missing:
            request_id = str(uuid.uuid4())
        
        return request_id or str(uuid.uuid4())
    
    def _get_header_value(self, request: Request, header_name: str) -> str:
        """获取请求头值"""
        return request.headers.get(header_name) or request.headers.get(header_name.lower())
    
    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP地址"""
        # 尝试从各种可能的头部获取真实IP
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            # X-Forwarded-For 可能包含多个IP，取第一个
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip
        
        # 如果没有代理头，使用客户端IP
        if hasattr(request, "client") and request.client:
            return request.client.host
        
        return "unknown"
