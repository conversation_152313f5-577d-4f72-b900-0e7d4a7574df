#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/7 09:35
# <AUTHOR> la<PERSON>o
# @Email   : <EMAIL>
# @File    : database.py
# @Update  : 2025/8/7 09:35 数据库配置

from typing import AsyncGenerator
from sqlalchemy.ext.asyncio import (
    AsyncSession,
    AsyncEngine,
    create_async_engine,
    async_sessionmaker
)
from sqlalchemy.orm import DeclarativeBase
from sqlalchemy.pool import NullPool
from .settings import settings


class Base(DeclarativeBase):
    """数据库模型基类"""
    pass


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self._engine: AsyncEngine | None = None
        self._session_factory: async_sessionmaker[AsyncSession] | None = None
    
    def create_engine(self) -> AsyncEngine:
        """创建数据库引擎"""
        if self._engine is None:
            # 根据环境选择不同的连接池配置
            if "test" in settings.database.name.lower():
                # 测试环境使用NullPool，避免连接池问题
                self._engine = create_async_engine(
                    settings.database_url,
                    echo=settings.database.echo,
                    poolclass=NullPool,
                )
            else:
                # 生产和开发环境使用连接池
                self._engine = create_async_engine(
                    settings.database_url,
                    echo=settings.database.echo,
                    pool_size=settings.database.pool_size,
                    max_overflow=settings.database.max_overflow,
                    pool_pre_ping=True,  # 连接前检查连接是否有效
                    pool_recycle=3600,   # 1小时后回收连接
                )
        return self._engine
    
    def create_session_factory(self) -> async_sessionmaker[AsyncSession]:
        """创建会话工厂"""
        if self._session_factory is None:
            engine = self.create_engine()
            self._session_factory = async_sessionmaker(
                engine,
                class_=AsyncSession,
                expire_on_commit=False,
                autoflush=False,
                autocommit=False,
            )
        return self._session_factory
    
    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """获取数据库会话"""
        session_factory = self.create_session_factory()
        async with session_factory() as session:
            try:
                yield session
                await session.commit()
            except Exception:
                await session.rollback()
                raise
            finally:
                await session.close()
    
    async def close(self):
        """关闭数据库连接"""
        if self._engine:
            await self._engine.dispose()
            self._engine = None
            self._session_factory = None
    
    async def create_tables(self):
        """创建所有表"""
        engine = self.create_engine()
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
    
    async def drop_tables(self):
        """删除所有表"""
        engine = self.create_engine()
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)


# 创建全局数据库管理器实例
db_manager = DatabaseManager()


# 依赖注入函数
async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """FastAPI依赖注入：获取数据库会话"""
    async for session in db_manager.get_session():
        yield session


# 便捷函数
def get_engine() -> AsyncEngine:
    """获取数据库引擎"""
    return db_manager.create_engine()


def get_session_factory() -> async_sessionmaker[AsyncSession]:
    """获取会话工厂"""
    return db_manager.create_session_factory()
