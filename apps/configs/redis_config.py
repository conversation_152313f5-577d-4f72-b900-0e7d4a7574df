#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/7 09:40
# <AUTHOR> la<PERSON>o
# @Email   : <EMAIL>
# @File    : redis_config.py
# @Update  : 2025/8/7 09:40 Redis配置

import json
from typing import Any, Optional, Union
import redis.asyncio as redis
from redis.asyncio import ConnectionPool
from .settings import settings


class RedisManager:
    """Redis管理器"""
    
    def __init__(self):
        self._pool: ConnectionPool | None = None
        self._redis: redis.Redis | None = None
    
    def create_pool(self) -> ConnectionPool:
        """创建Redis连接池"""
        if self._pool is None:
            self._pool = ConnectionPool.from_url(
                settings.redis_url,
                max_connections=settings.redis_max_connections,
                decode_responses=True,
                encoding="utf-8",
                retry_on_timeout=True,
                health_check_interval=30,
                password=settings.redis.password,  # 添加密码认证
            )
        return self._pool
    
    def get_redis(self) -> redis.Redis:
        """获取Redis客户端"""
        if self._redis is None:
            pool = self.create_pool()
            self._redis = redis.Redis(connection_pool=pool)
        return self._redis
    
    async def close(self):
        """关闭Redis连接"""
        if self._redis:
            await self._redis.close()
            self._redis = None
        if self._pool:
            await self._pool.disconnect()
            self._pool = None
    
    async def ping(self) -> bool:
        """检查Redis连接"""
        try:
            redis_client = self.get_redis()
            await redis_client.ping()
            return True
        except Exception:
            return False


class RedisService:
    """Redis服务类，提供常用的Redis操作"""
    
    def __init__(self, redis_manager: RedisManager):
        self.redis_manager = redis_manager
    
    @property
    def redis(self) -> redis.Redis:
        """获取Redis客户端"""
        return self.redis_manager.get_redis()
    
    # 字符串操作
    async def set(
        self, 
        key: str, 
        value: Any, 
        ex: Optional[int] = None,
        px: Optional[int] = None,
        nx: bool = False,
        xx: bool = False
    ) -> bool:
        """设置键值对"""
        if isinstance(value, (dict, list)):
            value = json.dumps(value, ensure_ascii=False)
        return await self.redis.set(key, value, ex=ex, px=px, nx=nx, xx=xx)
    
    async def get(self, key: str, default: Any = None) -> Any:
        """获取值"""
        value = await self.redis.get(key)
        if value is None:
            return default
        
        # 尝试解析JSON
        try:
            return json.loads(value)
        except (json.JSONDecodeError, TypeError):
            return value
    
    async def delete(self, *keys: str) -> int:
        """删除键"""
        return await self.redis.delete(*keys)
    
    async def exists(self, *keys: str) -> int:
        """检查键是否存在"""
        return await self.redis.exists(*keys)
    
    async def expire(self, key: str, time: int) -> bool:
        """设置过期时间"""
        return await self.redis.expire(key, time)
    
    async def ttl(self, key: str) -> int:
        """获取剩余生存时间"""
        return await self.redis.ttl(key)
    
    # 哈希操作
    async def hset(self, name: str, mapping: dict) -> int:
        """设置哈希字段"""
        # 将字典值转换为JSON字符串
        json_mapping = {}
        for k, v in mapping.items():
            if isinstance(v, (dict, list)):
                json_mapping[k] = json.dumps(v, ensure_ascii=False)
            else:
                json_mapping[k] = v
        return await self.redis.hset(name, mapping=json_mapping)
    
    async def hget(self, name: str, key: str, default: Any = None) -> Any:
        """获取哈希字段值"""
        value = await self.redis.hget(name, key)
        if value is None:
            return default
        
        try:
            return json.loads(value)
        except (json.JSONDecodeError, TypeError):
            return value
    
    async def hgetall(self, name: str) -> dict:
        """获取所有哈希字段"""
        data = await self.redis.hgetall(name)
        result = {}
        for k, v in data.items():
            try:
                result[k] = json.loads(v)
            except (json.JSONDecodeError, TypeError):
                result[k] = v
        return result
    
    async def hdel(self, name: str, *keys: str) -> int:
        """删除哈希字段"""
        return await self.redis.hdel(name, *keys)
    
    # 列表操作
    async def lpush(self, name: str, *values: Any) -> int:
        """从左侧推入列表"""
        json_values = []
        for value in values:
            if isinstance(value, (dict, list)):
                json_values.append(json.dumps(value, ensure_ascii=False))
            else:
                json_values.append(value)
        return await self.redis.lpush(name, *json_values)
    
    async def rpush(self, name: str, *values: Any) -> int:
        """从右侧推入列表"""
        json_values = []
        for value in values:
            if isinstance(value, (dict, list)):
                json_values.append(json.dumps(value, ensure_ascii=False))
            else:
                json_values.append(value)
        return await self.redis.rpush(name, *json_values)
    
    async def lpop(self, name: str, default: Any = None) -> Any:
        """从左侧弹出列表元素"""
        value = await self.redis.lpop(name)
        if value is None:
            return default
        
        try:
            return json.loads(value)
        except (json.JSONDecodeError, TypeError):
            return value
    
    async def rpop(self, name: str, default: Any = None) -> Any:
        """从右侧弹出列表元素"""
        value = await self.redis.rpop(name)
        if value is None:
            return default
        
        try:
            return json.loads(value)
        except (json.JSONDecodeError, TypeError):
            return value
    
    async def llen(self, name: str) -> int:
        """获取列表长度"""
        return await self.redis.llen(name)
    
    # 集合操作
    async def sadd(self, name: str, *values: Any) -> int:
        """添加集合成员"""
        json_values = []
        for value in values:
            if isinstance(value, (dict, list)):
                json_values.append(json.dumps(value, ensure_ascii=False))
            else:
                json_values.append(value)
        return await self.redis.sadd(name, *json_values)
    
    async def srem(self, name: str, *values: Any) -> int:
        """删除集合成员"""
        json_values = []
        for value in values:
            if isinstance(value, (dict, list)):
                json_values.append(json.dumps(value, ensure_ascii=False))
            else:
                json_values.append(value)
        return await self.redis.srem(name, *json_values)
    
    async def smembers(self, name: str) -> set:
        """获取所有集合成员"""
        members = await self.redis.smembers(name)
        result = set()
        for member in members:
            try:
                result.add(json.loads(member))
            except (json.JSONDecodeError, TypeError):
                result.add(member)
        return result
    
    async def scard(self, name: str) -> int:
        """获取集合成员数量"""
        return await self.redis.scard(name)


# 创建全局Redis管理器和服务实例
redis_manager = RedisManager()
redis_service = RedisService(redis_manager)


# 依赖注入函数
async def get_redis() -> redis.Redis:
    """FastAPI依赖注入：获取Redis客户端"""
    return redis_manager.get_redis()


async def get_redis_service() -> RedisService:
    """FastAPI依赖注入：获取Redis服务"""
    return redis_service
