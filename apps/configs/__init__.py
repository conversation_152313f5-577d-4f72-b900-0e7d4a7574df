#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/7 09:22
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : __init__.py
# @Update  : 2025/8/7 11:30 配置模块初始化

"""
配置模块

提供应用的所有配置管理功能，包括：
- YAML配置文件加载
- 数据库配置
- Redis配置
- 统一的配置管理
"""

from .settings import (
    settings,
    Settings,
    AppConfig,
    ServerConfig,
    DatabaseConfig,
    RedisConfig,
    JWTConfig,
    CORSConfig,
    LoggingConfig,
    UploadConfig,
    VectorConfig,
    GraphConfig,
    DockerConfig
)
from .database import (
    Base,
    DatabaseManager,
    db_manager,
    get_db,
    get_engine,
    get_session_factory
)
from .redis_config import (
    RedisManager,
    RedisService,
    redis_manager,
    redis_service,
    get_redis,
    get_redis_service
)
from .logging_config import (
    logger_manager,
    setup_logging,
    get_logger,
    get_app_logger,
    get_db_logger,
    get_redis_logger,
    get_api_logger,
    get_service_logger,
    get_task_logger
)

__all__ = [
    # 设置
    "settings",
    "Settings",
    "AppConfig",
    "ServerConfig",
    "DatabaseConfig",
    "RedisConfig",
    "JWTConfig",
    "CORSConfig",
    "LoggingConfig",
    "UploadConfig",
    "VectorConfig",
    "GraphConfig",
    "DockerConfig",

    # 数据库
    "Base",
    "DatabaseManager",
    "db_manager",
    "get_db",
    "get_engine",
    "get_session_factory",

    # Redis
    "RedisManager",
    "RedisService",
    "redis_manager",
    "redis_service",
    "get_redis",
    "get_redis_service",

    # 日志
    "logger_manager",
    "setup_logging",
    "get_logger",
    "get_app_logger",
    "get_db_logger",
    "get_redis_logger",
    "get_api_logger",
    "get_service_logger",
    "get_task_logger",
]
