[project]
name = "echonote"
version = "0.1.0"
description = "Add your description here"
requires-python = ">=3.12,<3.13"
dependencies = [
    "alembic>=1.16.4",
    "asyncpg>=0.30.0",
    "fastapi>=0.116.1",
    "httpx>=0.28.1",
    "pgvector>=0.4.1",
    "pydantic-settings>=2.10.1",
    "pytest>=8.4.1",
    "pytest-asyncio>=1.1.0",
    "pytest-cov>=6.2.1",
    "python-multipart>=0.0.20",
    "pyyaml>=6.0.2",
    "redis>=6.3.0",
    "sqlalchemy[asyncio]>=2.0.42",
    "uvicorn>=0.35.0",
]
