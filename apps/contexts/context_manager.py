#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/7 12:45
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : context_manager.py
# @Update  : 2025/8/7 12:45 全局上下文管理器

from contextvars import ContextVar
from typing import Any, Optional, Dict, TypeVar, Generic
from threading import Lock
import weakref


T = TypeVar('T')


class ContextVar_Generic(Generic[T]):
    """泛型上下文变量包装器"""
    
    def __init__(self, name: str, default: Optional[T] = None):
        self._var: ContextVar[Optional[T]] = ContextVar(name, default=default)
    
    def get(self) -> Optional[T]:
        return self._var.get()
    
    def set(self, value: T) -> None:
        self._var.set(value)
    
    def clear(self) -> None:
        self._var.set(None)


class ContextManager:
    """全局上下文管理器"""
    
    _instance = None
    _lock = Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, '_initialized'):
            self._contexts: Dict[str, ContextVar] = {}
            self._defaults: Dict[str, Any] = {}
            self._callbacks: Dict[str, list] = {}
            self._initialized = True
    
    def register_context(self, name: str, default: Any = None) -> ContextVar:
        """注册一个新的上下文变量"""
        if name in self._contexts:
            return self._contexts[name]
        
        context_var = ContextVar(name, default=default)
        self._contexts[name] = context_var
        self._defaults[name] = default
        self._callbacks[name] = []
        
        return context_var
    
    def get_context(self, name: str) -> Any:
        """获取上下文值"""
        if name not in self._contexts:
            return None
        
        return self._contexts[name].get()
    
    def set_context(self, name: str, value: Any) -> None:
        """设置上下文值"""
        if name not in self._contexts:
            self.register_context(name)

        old_value = self._contexts[name].get()
        self._contexts[name].set(value)

        # 触发回调
        callbacks = self._callbacks.get(name, [])
        for callback in callbacks:
            try:
                callback(name, old_value, value)
            except Exception as e:
                # 记录回调错误，但不影响设置操作
                print(f"Context callback error for {name}: {e}")
    
    def clear_context(self, name: str) -> None:
        """清除上下文值"""
        if name in self._contexts:
            default_value = self._defaults.get(name)
            self._contexts[name].set(default_value)
    
    def clear_all_contexts(self) -> None:
        """清除所有上下文值"""
        for name in self._contexts:
            self.clear_context(name)
    
    def add_callback(self, name: str, callback) -> None:
        """添加上下文变更回调"""
        if name not in self._callbacks:
            self._callbacks[name] = []
        
        self._callbacks[name].append(callback)
    
    def remove_callback(self, name: str, callback) -> None:
        """移除上下文变更回调"""
        if name in self._callbacks and callback in self._callbacks[name]:
            self._callbacks[name].remove(callback)
    
    def get_all_contexts(self) -> Dict[str, Any]:
        """获取所有上下文值"""
        result = {}
        for name, context_var in self._contexts.items():
            result[name] = context_var.get()
        return result
    
    def list_context_names(self) -> list:
        """列出所有注册的上下文名称"""
        return list(self._contexts.keys())
    
    def context_exists(self, name: str) -> bool:
        """检查上下文是否存在"""
        return name in self._contexts
    
    def get_context_info(self, name: str) -> Optional[Dict[str, Any]]:
        """获取上下文信息"""
        if name not in self._contexts:
            return None
        
        return {
            "name": name,
            "current_value": self._contexts[name].get(),
            "default_value": self._defaults.get(name),
            "callback_count": len(self._callbacks.get(name, []))
        }


# 全局上下文管理器实例
context_manager = ContextManager()

# 预注册一些常用的上下文
context_manager.register_context("request_id", default=None)
context_manager.register_context("user_id", default=None)
context_manager.register_context("trace_id", default=None)
context_manager.register_context("session_id", default=None)
context_manager.register_context("tenant_id", default=None)
context_manager.register_context("correlation_id", default=None)


# 便捷函数
def get_context(name: str) -> Any:
    """获取上下文值的便捷函数"""
    return context_manager.get_context(name)


def set_context(name: str, value: Any) -> None:
    """设置上下文值的便捷函数"""
    context_manager.set_context(name, value)


def clear_context(name: str) -> None:
    """清除上下文值的便捷函数"""
    context_manager.clear_context(name)


def register_context(name: str, default: Any = None) -> ContextVar:
    """注册上下文的便捷函数"""
    return context_manager.register_context(name, default)


def add_context_callback(name: str, callback) -> None:
    """添加上下文回调的便捷函数"""
    context_manager.add_callback(name, callback)


# 特定上下文的便捷函数
def get_request_id_from_context() -> Optional[str]:
    """从上下文获取请求ID"""
    return get_context("request_id")


def set_request_id_to_context(request_id: str) -> None:
    """设置请求ID到上下文"""
    set_context("request_id", request_id)


def get_user_id_from_context() -> Optional[str]:
    """从上下文获取用户ID"""
    return get_context("user_id")


def set_user_id_to_context(user_id: str) -> None:
    """设置用户ID到上下文"""
    set_context("user_id", user_id)


def get_trace_id() -> Optional[str]:
    """获取追踪ID"""
    return get_context("trace_id")


def set_trace_id(trace_id: str) -> None:
    """设置追踪ID"""
    set_context("trace_id", trace_id)
