#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/7 12:40
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : request_context.py
# @Update  : 2025/8/7 12:40 请求上下文管理

import uuid
from contextvars import ContextVar
from typing import Optional, Dict, Any
from datetime import datetime
from dataclasses import dataclass, field


@dataclass
class RequestContext:
    """请求上下文数据类"""
    request_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    user_id: Optional[str] = None
    username: Optional[str] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    method: Optional[str] = None
    path: Optional[str] = None
    query_params: Optional[Dict[str, Any]] = None
    headers: Optional[Dict[str, str]] = None
    start_time: datetime = field(default_factory=datetime.utcnow)
    extra: Optional[Dict[str, Any]] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "request_id": self.request_id,
            "user_id": self.user_id,
            "username": self.username,
            "ip_address": self.ip_address,
            "user_agent": self.user_agent,
            "method": self.method,
            "path": self.path,
            "query_params": self.query_params,
            "headers": self.headers,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "extra": self.extra
        }
    
    def get_duration(self) -> float:
        """获取请求持续时间（秒）"""
        if self.start_time:
            return (datetime.utcnow() - self.start_time).total_seconds()
        return 0.0
    
    def add_extra(self, key: str, value: Any):
        """添加额外信息"""
        if self.extra is None:
            self.extra = {}
        self.extra[key] = value
    
    def get_extra(self, key: str, default: Any = None) -> Any:
        """获取额外信息"""
        if self.extra is None:
            return default
        return self.extra.get(key, default)


# 使用 ContextVar 来存储请求上下文
_request_context: ContextVar[Optional[RequestContext]] = ContextVar(
    'request_context', 
    default=None
)


def get_request_context() -> Optional[RequestContext]:
    """获取当前请求上下文"""
    return _request_context.get()


def set_request_context(context: RequestContext) -> None:
    """设置请求上下文"""
    _request_context.set(context)


def clear_request_context() -> None:
    """清除请求上下文"""
    _request_context.set(None)


def get_request_id() -> Optional[str]:
    """获取当前请求ID"""
    context = get_request_context()
    return context.request_id if context else None


def get_user_id() -> Optional[str]:
    """获取当前用户ID"""
    context = get_request_context()
    return context.user_id if context else None


def get_username() -> Optional[str]:
    """获取当前用户名"""
    context = get_request_context()
    return context.username if context else None


def get_ip_address() -> Optional[str]:
    """获取当前请求IP地址"""
    context = get_request_context()
    return context.ip_address if context else None


def get_user_agent() -> Optional[str]:
    """获取当前请求User-Agent"""
    context = get_request_context()
    return context.user_agent if context else None


def get_request_method() -> Optional[str]:
    """获取当前请求方法"""
    context = get_request_context()
    return context.method if context else None


def get_request_path() -> Optional[str]:
    """获取当前请求路径"""
    context = get_request_context()
    return context.path if context else None


def get_request_duration() -> float:
    """获取当前请求持续时间"""
    context = get_request_context()
    return context.get_duration() if context else 0.0


def add_request_extra(key: str, value: Any) -> None:
    """添加请求额外信息"""
    context = get_request_context()
    if context:
        context.add_extra(key, value)


def get_request_extra(key: str, default: Any = None) -> Any:
    """获取请求额外信息"""
    context = get_request_context()
    return context.get_extra(key, default) if context else default


def create_request_context(
    request_id: Optional[str] = None,
    user_id: Optional[str] = None,
    username: Optional[str] = None,
    ip_address: Optional[str] = None,
    user_agent: Optional[str] = None,
    method: Optional[str] = None,
    path: Optional[str] = None,
    query_params: Optional[Dict[str, Any]] = None,
    headers: Optional[Dict[str, str]] = None,
    **extra
) -> RequestContext:
    """创建请求上下文"""
    context = RequestContext(
        request_id=request_id or str(uuid.uuid4()),
        user_id=user_id,
        username=username,
        ip_address=ip_address,
        user_agent=user_agent,
        method=method,
        path=path,
        query_params=query_params,
        headers=headers,
        extra=extra if extra else None
    )
    return context
