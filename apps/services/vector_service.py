#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/7 11:10
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : vector_service.py
# @Update  : 2025/8/7 11:10 向量数据库服务

from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, text, func
from sqlalchemy.orm import selectinload
import numpy as np

from models.embedding import Embedding, EmbeddingCollection
from configs.settings import settings
from configs.logging_config import get_service_logger

# 获取服务日志器
logger = get_service_logger()


class VectorService:
    """向量数据库服务类"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_embedding(
        self,
        content: str,
        embedding: List[float],
        title: Optional[str] = None,
        meta_data: Optional[Dict[str, Any]] = None,
        source: Optional[str] = None,
        source_id: Optional[str] = None,
        category: Optional[str] = None,
        tags: Optional[List[str]] = None
    ) -> Embedding:
        """创建向量嵌入"""
        
        # 计算统计信息
        char_count = Embedding.calculate_char_count(content)
        token_count = Embedding.estimate_token_count(content)
        
        embedding_obj = Embedding(
            content=content,
            title=title,
            embedding=embedding,
            meta_data=meta_data or {},
            source=source,
            source_id=source_id,
            category=category,
            tags=tags or [],
            char_count=char_count,
            token_count=token_count
        )
        
        self.db.add(embedding_obj)
        await self.db.commit()
        await self.db.refresh(embedding_obj)
        
        return embedding_obj
    
    async def get_embedding(self, embedding_id: int) -> Optional[Embedding]:
        """获取向量嵌入"""
        result = await self.db.execute(
            select(Embedding).where(Embedding.id == embedding_id)
        )
        return result.scalar_one_or_none()
    
    async def search_similar_embeddings(
        self,
        query_embedding: List[float],
        limit: int = 10,
        threshold: float = 0.8,
        category: Optional[str] = None,
        source: Optional[str] = None
    ) -> List[Tuple[Embedding, float]]:
        """搜索相似的向量嵌入"""
        
        # 构建基础查询
        query = select(Embedding)
        
        # 添加过滤条件
        if category:
            query = query.where(Embedding.category == category)
        if source:
            query = query.where(Embedding.source == source)
        
        # 添加向量相似度搜索
        if settings.vector_distance_metric == "cosine":
            # 余弦相似度
            distance_expr = Embedding.embedding.cosine_distance(query_embedding)
            similarity_expr = 1 - distance_expr
        elif settings.vector_distance_metric == "l2":
            # 欧几里得距离
            distance_expr = Embedding.embedding.l2_distance(query_embedding)
            similarity_expr = 1 / (1 + distance_expr)
        else:
            # 内积
            similarity_expr = Embedding.embedding.inner_product(query_embedding)
            distance_expr = -similarity_expr
        
        # 添加相似度阈值过滤
        query = query.where(similarity_expr >= threshold)
        
        # 按相似度排序
        query = query.order_by(distance_expr.asc()).limit(limit)
        
        # 执行查询
        result = await self.db.execute(query)
        embeddings = result.scalars().all()
        
        # 计算相似度分数
        results = []
        for embedding in embeddings:
            if embedding.embedding is not None:
                try:
                    query_vec = np.array(query_embedding)
                    embed_vec = np.array(embedding.embedding)

                    if settings.vector_distance_metric == "cosine":
                        # 余弦相似度
                        dot_product = np.dot(query_vec, embed_vec)
                        norm_product = np.linalg.norm(query_vec) * np.linalg.norm(embed_vec)
                        if norm_product > 0:
                            similarity = dot_product / norm_product
                        else:
                            similarity = 0.0
                    elif settings.vector_distance_metric == "l2":
                        # 欧几里得距离转相似度
                        distance = np.linalg.norm(query_vec - embed_vec)
                        similarity = 1 / (1 + distance)
                    else:
                        # 内积
                        similarity = np.dot(query_vec, embed_vec)

                    results.append((embedding, float(similarity)))
                except Exception as e:
                    logger.warning(f"计算相似度时出错: {e}")
                    continue
        
        return results
    
    async def update_embedding(
        self,
        embedding_id: int,
        content: Optional[str] = None,
        embedding: Optional[List[float]] = None,
        title: Optional[str] = None,
        meta_data: Optional[Dict[str, Any]] = None,
        category: Optional[str] = None,
        tags: Optional[List[str]] = None
    ) -> Optional[Embedding]:
        """更新向量嵌入"""
        
        embedding_obj = await self.get_embedding(embedding_id)
        if not embedding_obj:
            return None
        
        if content is not None:
            embedding_obj.content = content
            embedding_obj.char_count = Embedding.calculate_char_count(content)
            embedding_obj.token_count = Embedding.estimate_token_count(content)
        
        if embedding is not None:
            embedding_obj.embedding = embedding
        
        if title is not None:
            embedding_obj.title = title
        
        if meta_data is not None:
            embedding_obj.meta_data = meta_data
        
        if category is not None:
            embedding_obj.category = category
        
        if tags is not None:
            embedding_obj.tags = tags
        
        await self.db.commit()
        await self.db.refresh(embedding_obj)
        
        return embedding_obj
    
    async def delete_embedding(self, embedding_id: int) -> bool:
        """删除向量嵌入"""
        embedding_obj = await self.get_embedding(embedding_id)
        if not embedding_obj:
            return False
        
        await self.db.delete(embedding_obj)
        await self.db.commit()
        
        return True
    
    async def create_collection(
        self,
        name: str,
        description: Optional[str] = None,
        dimension: int = None,
        distance_metric: str = None,
        meta_data: Optional[Dict[str, Any]] = None
    ) -> EmbeddingCollection:
        """创建向量集合"""
        
        collection = EmbeddingCollection(
            name=name,
            description=description,
            dimension=dimension or settings.vector_dimension,
            distance_metric=distance_metric or settings.vector_distance_metric,
            meta_data=meta_data or {}
        )
        
        self.db.add(collection)
        await self.db.commit()
        await self.db.refresh(collection)
        
        return collection
    
    async def get_collection(self, collection_id: int) -> Optional[EmbeddingCollection]:
        """获取向量集合"""
        result = await self.db.execute(
            select(EmbeddingCollection).where(EmbeddingCollection.id == collection_id)
        )
        return result.scalar_one_or_none()
    
    async def list_collections(self, limit: int = 100, offset: int = 0) -> List[EmbeddingCollection]:
        """列出向量集合"""
        result = await self.db.execute(
            select(EmbeddingCollection)
            .order_by(EmbeddingCollection.created_at.desc())
            .limit(limit)
            .offset(offset)
        )
        return result.scalars().all()
    
    async def get_embedding_stats(self) -> Dict[str, Any]:
        """获取向量数据库统计信息"""
        
        # 总数统计
        total_embeddings = await self.db.scalar(
            select(func.count(Embedding.id))
        )
        
        total_collections = await self.db.scalar(
            select(func.count(EmbeddingCollection.id))
        )
        
        # 按分类统计
        category_stats = await self.db.execute(
            select(Embedding.category, func.count(Embedding.id))
            .group_by(Embedding.category)
            .order_by(func.count(Embedding.id).desc())
        )
        
        # 按来源统计
        source_stats = await self.db.execute(
            select(Embedding.source, func.count(Embedding.id))
            .group_by(Embedding.source)
            .order_by(func.count(Embedding.id).desc())
        )
        
        return {
            "total_embeddings": total_embeddings,
            "total_collections": total_collections,
            "category_distribution": dict(category_stats.all()),
            "source_distribution": dict(source_stats.all()),
            "vector_dimension": settings.vector_dimension,
            "distance_metric": settings.vector_distance_metric,
            "index_type": settings.vector_index_type
        }
