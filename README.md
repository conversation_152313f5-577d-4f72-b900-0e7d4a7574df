# EchoNote

EchoNote是一个基于FastAPI、PostgreSQL、Redis的现代化Web应用，支持向量数据库和图数据库功能。

## 功能特性

- ✅ FastAPI框架，支持异步操作
- ✅ PostgreSQL数据库，使用SQLAlchemy ORM
- ✅ **pgvector扩展**，支持向量数据库功能
- ✅ **Apache AGE扩展**，支持图数据库功能
- ✅ Redis缓存支持，带密码认证
- ✅ 环境配置管理（开发/生产/测试）
- ✅ 数据库迁移支持（Alembic）
- ✅ Docker Compose快速部署
- ✅ 健康检查接口，包含扩展状态检查
- ✅ CORS支持
- ✅ 日志配置
- ✅ 向量相似度搜索
- ✅ 图查询和路径分析

## 快速开始

### 方式一：一键启动（推荐）

```bash
# 克隆项目
git clone <repository-url>
cd EchoNote

# 运行快速启动脚本
./quick_start.sh
```

### 方式二：手动启动

#### 1. 环境准备

确保你的系统已安装：
- Python 3.12+
- Docker 和 Docker Compose
- uv (Python包管理器)

#### 2. 克隆项目

```bash
git clone <repository-url>
cd EchoNote
```

#### 3. 启动数据库服务

```bash
# 启动PostgreSQL（支持pgvector和Apache AGE）和Redis
docker-compose up -d postgres redis

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f postgres redis
```

#### 4. 配置应用

```bash
cd apps

# 查看当前配置
uv run python config_manager.py show

# 验证配置
uv run python config_manager.py validate

# 创建自定义配置（可选）
uv run python config_manager.py sample
# 然后编辑 config.sample.yaml 并重命名为 config.yaml
```

#### 5. 安装依赖

```bash
cd apps
uv sync
```

#### 6. 运行数据库迁移

```bash
cd apps

# 创建初始迁移文件（如果不存在）
uv run alembic revision --autogenerate -m "Initial migration with vector and graph support"

# 执行迁移
uv run alembic upgrade head
```

**重要说明**：
- 数据库表结构完全由Alembic管理
- 应用启动时不会自动创建表
- 必须先运行迁移才能正常使用应用

#### 7. 启动应用

```bash
# 开发模式启动
uv run python run.py

# 或者使用uvicorn直接启动
uv run uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

#### 8. 访问应用

- API文档: http://localhost:8000/docs
- 健康检查: http://localhost:8000/health
- PostgreSQL管理: http://localhost:5050 (<EMAIL> / admin)
- Redis管理: http://localhost:8081

#### 9. 验证扩展功能

```bash
# 连接到PostgreSQL容器
docker exec -it echo_note-postgres-dev psql -U echo_note -d echo_note

# 检查已安装的扩展
SELECT extname, extversion FROM pg_extension WHERE extname IN ('vector', 'age');

# 测试向量功能
SELECT '[1,2,3]'::vector;

# 测试图功能
SELECT create_graph('test_graph');
```

## 功能测试

### 自动化测试

运行完整的功能测试脚本：

```bash
cd apps
uv run python test_features.py
```

测试脚本会验证：
- ✅ 数据库连接
- ✅ Redis连接和基本操作
- ✅ PostgreSQL扩展（pgvector、Apache AGE）
- ✅ 向量数据库功能（创建、搜索、统计）
- ✅ 图数据库功能（节点、边、查询）

### 手动测试

#### 测试健康检查接口

```bash
curl http://localhost:8000/health
```

预期响应：
```json
{
  "status": "healthy",
  "app_name": "EchoNote",
  "version": "0.1.0",
  "environment": "development",
  "services": {
    "database": "healthy",
    "redis": "healthy",
    "vector_db": {
      "status": "healthy",
      "stats": {
        "total_embeddings": 0,
        "vector_dimension": 1536,
        "distance_metric": "cosine"
      }
    },
    "graph_db": {
      "status": "healthy",
      "stats": {
        "node_count": 0,
        "edge_count": 0
      }
    }
  },
  "extensions": {
    "pgvector": "enabled",
    "apache_age": "enabled"
  }
}
```

## 项目结构

```
EchoNote/
├── apps/                   # 应用主目录
│   ├── configs/           # 配置模块
│   │   ├── __init__.py
│   │   ├── settings.py    # 基础配置
│   │   ├── database.py    # 数据库配置
│   │   ├── redis_config.py # Redis配置
│   │   ├── development.py # 开发环境配置
│   │   ├── production.py  # 生产环境配置
│   │   ├── testing.py     # 测试环境配置
│   │   └── config_factory.py # 配置工厂
│   ├── models/            # 数据模型
│   │   ├── __init__.py
│   │   └── user.py        # 用户模型
│   ├── apis/              # API路由
│   ├── services/          # 业务逻辑
│   ├── migrations/        # 数据库迁移
│   ├── main.py           # 应用入口
│   ├── run.py            # 启动脚本
│   └── pyproject.toml    # 项目配置
├── front_web/             # 前端代码
├── docker-compose.yml     # Docker编排
├── .env.example          # 环境变量模板
└── README.md             # 项目说明
```

## 配置说明

### YAML配置文件

EchoNote使用YAML配置文件管理所有设置，配置文件位于 `apps/config.yaml`：

```yaml
# 应用配置
app:
  name: "EchoNote"
  version: "0.1.0"
  description: "EchoNote API服务"
  debug: true

# 服务器配置
server:
  host: "0.0.0.0"
  port: 8000

# 数据库配置
database:
  host: "localhost"
  port: 5433
  name: "echo_note"
  user: "echo_note"
  password: "password"
  pool_size: 10
  max_overflow: 20

# Redis配置
redis:
  host: "localhost"
  port: 6380
  db: 0
  password: "password"
  max_connections: 10

# 向量数据库配置
vector:
  dimension: 1536
  index_type: "hnsw"
  distance_metric: "cosine"

# 图数据库配置
graph:
  name: "echo_note_graph"
  age_schema: "ag_catalog"
```

### 配置管理工具

使用内置的配置管理工具：

```bash
cd apps

# 显示当前配置
uv run python config_manager.py show

# 验证配置
uv run python config_manager.py validate

# 导出配置
uv run python config_manager.py export config_backup.yaml

# 创建示例配置
uv run python config_manager.py sample

# 查看日志信息
uv run python config_manager.py logs

# 测试日志系统
uv run python config_manager.py test-logs
```

### 日志系统

EchoNote集成了统一的日志管理系统：

```python
from configs import get_app_logger, get_db_logger, get_api_logger

# 获取不同类型的日志器
app_logger = get_app_logger()      # 应用日志
db_logger = get_db_logger()        # 数据库日志
api_logger = get_api_logger()      # API日志

# 使用日志器
app_logger.info("应用启动成功")
db_logger.warning("数据库连接池接近满载")
api_logger.error("API请求失败")
```

**日志配置**：
- 日志文件保存在 `logs/` 目录
- `app.log`: 应用主日志
- `error.log`: 错误日志
- `access.log`: 访问日志
- 支持日志轮转（10MB，保留5个备份）
- 可配置日志级别和格式

## 数据库管理

### 使用数据库管理工具（推荐）

项目提供了便捷的数据库管理脚本：

```bash
# 查看帮助
./manage_db.sh help

# 初始化数据库（首次使用）
./manage_db.sh init

# 创建新迁移
./manage_db.sh migrate "添加新功能"

# 执行迁移
./manage_db.sh upgrade

# 查看数据库状态
./manage_db.sh status

# 查看迁移历史
./manage_db.sh history

# 回退迁移（谨慎使用）
./manage_db.sh downgrade -1
```

### 直接使用Alembic

```bash
cd apps

# 创建迁移文件
uv run alembic revision --autogenerate -m "描述信息"

# 执行迁移
uv run alembic upgrade head

# 查看当前版本
uv run alembic current

# 查看迁移历史
uv run alembic history
```

### 重要提醒

- ⚠️ **数据库表结构完全由Alembic管理**
- ⚠️ **应用启动时不会自动创建表**
- ⚠️ **必须先运行迁移才能正常使用应用**
- ⚠️ **生产环境请谨慎执行降级操作**

## API接口

### 基础接口

- `GET /` - 根路径，返回应用信息
- `GET /health` - 健康检查，检查数据库和Redis连接状态
- `GET /hello/{name}` - 问候接口

### 健康检查响应示例

```json
{
  "status": "healthy",
  "app_name": "EchoNote",
  "version": "0.1.0",
  "environment": "development",
  "services": {
    "database": "healthy",
    "redis": "healthy"
  }
}
```

## 开发指南

### 添加新的模型

1. 在`models/`目录下创建新的模型文件
2. 在`models/__init__.py`中导入新模型
3. 创建数据库迁移：`uv run alembic revision --autogenerate -m "Add new model"`
4. 执行迁移：`uv run alembic upgrade head`

### 添加新的API

1. 在`apis/`目录下创建路由文件
2. 在`main.py`中注册路由

### 使用Redis

```python
from configs import get_redis_service

async def example_function(redis_service = Depends(get_redis_service)):
    # 设置值
    await redis_service.set("key", "value", ex=3600)

    # 获取值
    value = await redis_service.get("key")

    # 删除值
    await redis_service.delete("key")
```

### 使用向量数据库

```python
from services.vector_service import VectorService
from configs import get_db

async def vector_example(db = Depends(get_db)):
    vector_service = VectorService(db)

    # 创建向量嵌入
    embedding = await vector_service.create_embedding(
        content="这是一段示例文本",
        embedding=[0.1, 0.2, 0.3, ...],  # 1536维向量
        title="示例文档",
        category="文档",
        tags=["示例", "测试"]
    )

    # 搜索相似向量
    query_embedding = [0.1, 0.2, 0.3, ...]
    similar_embeddings = await vector_service.search_similar_embeddings(
        query_embedding=query_embedding,
        limit=10,
        threshold=0.8
    )

    for embedding, similarity in similar_embeddings:
        print(f"相似度: {similarity:.3f}, 内容: {embedding.content[:50]}...")
```

### 使用图数据库

```python
from services.graph_service import GraphService
from configs import get_db

async def graph_example(db = Depends(get_db)):
    graph_service = GraphService(db)

    # 创建节点
    user_id = await graph_service.create_node(
        label="User",
        properties={"name": "Alice", "age": 30}
    )

    doc_id = await graph_service.create_node(
        label="Document",
        properties={"title": "重要文档", "type": "PDF"}
    )

    # 创建关系
    await graph_service.create_edge(
        source_node_id=user_id,
        target_node_id=doc_id,
        label="OWNS",
        properties={"created_at": "2025-08-07"}
    )

    # 查找路径
    paths = await graph_service.find_paths(
        start_node_id=user_id,
        end_node_id=doc_id,
        max_depth=3
    )

    # 获取邻居节点
    neighbors = await graph_service.get_node_neighbors(
        node_id=user_id,
        direction="out"
    )
```

## 部署

### Docker部署

```bash
# 构建并启动所有服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 生产环境部署

1. 设置环境变量：`ENVIRONMENT=production`
2. 配置生产环境的数据库和Redis连接
3. 设置强密码和安全密钥
4. 配置HTTPS和防火墙
5. 设置日志收集和监控

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查PostgreSQL服务是否启动
   - 验证数据库连接参数
   - 检查网络连接

2. **Redis连接失败**
   - 检查Redis服务是否启动
   - 验证Redis连接参数

3. **迁移失败**
   - 检查数据库权限
   - 查看迁移日志
   - 手动修复数据库状态

4. **Alembic迁移问题**

   如果 `alembic revision` 没有生成文件或生成空文件：

   ```bash
   # 检查模型是否正确导入
   cd apps
   uv run python -c "from models import *; print('模型导入成功')"

   # 手动安装数据库扩展
   ./manage_db.sh extensions

   # 重新生成迁移
   ./manage_db.sh migrate "重新生成迁移"
   ```

5. **Apache AGE扩展问题**

   如果遇到 `create_graph` 函数不存在的错误：

   ```bash
   # 方法1：使用数据库管理工具
   ./manage_db.sh extensions

   # 方法2：手动进入容器设置
   docker exec -it echo_note-postgres-dev psql -U echo_note -d echo_note
   ```

   在PostgreSQL中执行：
   ```sql
   -- 安装扩展
   CREATE EXTENSION IF NOT EXISTS vector;
   CREATE EXTENSION IF NOT EXISTS age;

   -- 加载AGE扩展
   LOAD 'age';

   -- 设置搜索路径
   SET search_path = ag_catalog, "$user", public;

   -- 创建图数据库
   SELECT ag_catalog.create_graph('echo_note_graph');
   ```

5. **pgvector扩展问题**

   检查向量扩展是否正确安装：
   ```sql
   -- 检查扩展
   SELECT * FROM pg_extension WHERE extname = 'vector';

   -- 测试向量操作
   SELECT '[1,2,3]'::vector;
   ```

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

MIT License
