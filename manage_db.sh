#!/bin/bash

# 数据库管理脚本
# 用于管理 EchoNote 项目的数据库迁移

set -e

cd apps

function show_help() {
    echo "EchoNote 数据库管理工具"
    echo "======================="
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  init        初始化数据库（创建初始迁移）"
    echo "  migrate     创建新的迁移文件"
    echo "  upgrade     执行迁移（升级到最新版本）"
    echo "  downgrade   降级数据库"
    echo "  current     显示当前迁移版本"
    echo "  history     显示迁移历史"
    echo "  reset       重置数据库（危险操作）"
    echo "  status      显示数据库状态"
    echo "  extensions  安装数据库扩展"
    echo "  help        显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 init                    # 初始化数据库"
    echo "  $0 migrate \"添加用户表\"    # 创建新迁移"
    echo "  $0 upgrade                 # 执行所有待执行的迁移"
    echo "  $0 downgrade -1            # 回退一个版本"
}

function check_dependencies() {
    if ! command -v uv &> /dev/null; then
        echo "❌ uv 未安装，请先安装 uv"
        exit 1
    fi
    
    if [ ! -f "pyproject.toml" ]; then
        echo "❌ 请在 apps 目录下运行此脚本"
        exit 1
    fi
}

function check_db_connection() {
    echo "🔍 检查数据库连接..."
    if ! docker ps | grep -q echo_note-postgres-dev; then
        echo "❌ PostgreSQL 容器未运行，请先启动："
        echo "docker-compose up -d postgres"
        exit 1
    fi
    echo "✅ 数据库连接正常"
}

function install_extensions() {
    echo "🔧 安装数据库扩展..."

    # 安装 pgvector 扩展
    echo "📦 安装 pgvector 扩展..."
    docker exec echo_note-postgres-dev psql -U echo_note -d echo_note -c "CREATE EXTENSION IF NOT EXISTS vector;" || {
        echo "❌ pgvector 扩展安装失败"
        return 1
    }

    # 安装 Apache AGE 扩展
    echo "📦 安装 Apache AGE 扩展..."
    docker exec echo_note-postgres-dev psql -U echo_note -d echo_note -c "CREATE EXTENSION IF NOT EXISTS age;" || {
        echo "⚠️ Apache AGE 扩展安装失败，但不影响其他功能"
    }

    # 为测试数据库也安装扩展
    echo "📦 为测试数据库安装扩展..."
    docker exec echo_note-postgres-dev psql -U echo_note -d echo_note_test -c "CREATE EXTENSION IF NOT EXISTS vector;" 2>/dev/null || true
    docker exec echo_note-postgres-dev psql -U echo_note -d echo_note_test -c "CREATE EXTENSION IF NOT EXISTS age;" 2>/dev/null || true

    echo "✅ 扩展安装完成"
}

function init_db() {
    echo "🚀 初始化数据库..."

    # 首先安装扩展
    install_extensions

    # 检查是否已有迁移文件
    if [ -d "migrations/versions" ] && [ "$(ls -A migrations/versions 2>/dev/null)" ]; then
        echo "⚠️ 迁移文件已存在，跳过创建"
        echo "如需重新初始化，请先运行: $0 reset"
    else
        echo "📝 创建初始迁移..."
        uv run alembic revision --autogenerate -m "Initial migration with vector and graph support"
    fi

    echo "🚀 执行迁移..."
    uv run alembic upgrade head

    echo "✅ 数据库初始化完成"
}

function create_migration() {
    local message="$1"
    if [ -z "$message" ]; then
        echo "❌ 请提供迁移描述"
        echo "用法: $0 migrate \"迁移描述\""
        exit 1
    fi
    
    echo "📝 创建迁移: $message"
    uv run alembic revision --autogenerate -m "$message"
    echo "✅ 迁移文件创建完成"
}

function upgrade_db() {
    local target="${1:-head}"
    echo "🚀 执行数据库迁移到: $target"
    uv run alembic upgrade "$target"
    echo "✅ 数据库迁移完成"
}

function downgrade_db() {
    local target="$1"
    if [ -z "$target" ]; then
        echo "❌ 请指定降级目标"
        echo "用法: $0 downgrade <版本号|相对版本>"
        echo "示例: $0 downgrade -1  # 回退一个版本"
        exit 1
    fi
    
    echo "⚠️ 警告：即将降级数据库到: $target"
    read -p "确认继续？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "🔄 执行数据库降级..."
        uv run alembic downgrade "$target"
        echo "✅ 数据库降级完成"
    else
        echo "❌ 操作已取消"
    fi
}

function show_current() {
    echo "📋 当前数据库版本:"
    uv run alembic current
}

function show_history() {
    echo "📚 迁移历史:"
    uv run alembic history --verbose
}

function reset_db() {
    echo "⚠️ 危险操作：即将重置数据库"
    echo "这将删除所有数据和迁移文件！"
    read -p "确认继续？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "🗑️ 删除迁移文件..."
        rm -rf migrations/versions/*
        
        echo "🔄 重置数据库..."
        # 这里可以添加删除所有表的逻辑
        echo "请手动清理数据库表，然后运行: $0 init"
        
        echo "✅ 重置完成"
    else
        echo "❌ 操作已取消"
    fi
}

function show_status() {
    echo "📊 数据库状态"
    echo "=============="
    
    echo ""
    echo "🔍 当前版本:"
    uv run alembic current
    
    echo ""
    echo "📋 待执行的迁移:"
    uv run alembic show head
    
    echo ""
    echo "🗃️ 数据库扩展状态:"
    docker exec echo_note-postgres-dev psql -U echo_note -d echo_note -c "SELECT extname, extversion FROM pg_extension WHERE extname IN ('vector', 'age');" 2>/dev/null || echo "❌ 无法连接数据库"
}

# 主逻辑
check_dependencies

case "${1:-help}" in
    "init")
        check_db_connection
        init_db
        ;;
    "migrate")
        check_db_connection
        create_migration "$2"
        ;;
    "upgrade")
        check_db_connection
        upgrade_db "$2"
        ;;
    "downgrade")
        check_db_connection
        downgrade_db "$2"
        ;;
    "current")
        check_db_connection
        show_current
        ;;
    "history")
        show_history
        ;;
    "reset")
        reset_db
        ;;
    "status")
        check_db_connection
        show_status
        ;;
    "extensions")
        check_db_connection
        install_extensions
        ;;
    "help"|*)
        show_help
        ;;
esac
