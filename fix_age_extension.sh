#!/bin/bash

# 修复 Apache AGE 扩展问题的脚本

set -e

echo "🔧 修复 Apache AGE 扩展问题"
echo "=========================="

# 停止现有容器
echo "🛑 停止现有容器..."
docker-compose down

# 删除现有的 PostgreSQL 数据卷（这会清除所有数据）
echo "🗑️ 清理 PostgreSQL 数据卷..."
docker volume rm echonote_postgres_data 2>/dev/null || true

# 重新构建 PostgreSQL 镜像
echo "🔨 重新构建 PostgreSQL 镜像..."
docker-compose build --no-cache postgres

# 启动服务
echo "🚀 启动服务..."
docker-compose up -d postgres redis

# 等待 PostgreSQL 启动
echo "⏳ 等待 PostgreSQL 启动..."
sleep 30

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose ps

# 检查 PostgreSQL 日志
echo "📋 检查 PostgreSQL 日志..."
docker-compose logs postgres | tail -20

# 测试连接和扩展
echo "🧪 测试数据库连接和扩展..."
docker exec echo_note-postgres-dev psql -U echo_note -d echo_note -c "SELECT extname, extversion FROM pg_extension WHERE extname IN ('vector', 'age');"

# 测试 AGE 功能
echo "🧪 测试 AGE 功能..."
docker exec echo_note-postgres-dev psql -U echo_note -d echo_note -c "SELECT ag_catalog.create_graph('test_graph');" || echo "⚠️ AGE 功能可能需要手动配置"

echo "✅ 修复完成！"
echo ""
echo "如果仍有问题，请检查："
echo "1. PostgreSQL 日志: docker-compose logs postgres"
echo "2. 手动连接数据库: docker exec -it echo_note-postgres-dev psql -U echo_note -d echo_note"
echo "3. 检查扩展状态: SELECT * FROM pg_available_extensions WHERE name IN ('vector', 'age');"
